'use client'
import type { FC } from 'react'
import React, { useEffect, useMemo, useState } from 'react'
import useSWR from 'swr'
import { useTranslation } from 'react-i18next'
import { useRouter } from 'next/navigation'
import { omit } from 'lodash-es'
import { Pagination } from 'antd'
import { changeIndexStatus, judgeIsIndexing } from '../utils'
import List from './list'
import RetryButton from './retry-button'
import {
  fetchDocuments,
  fetchDocumentsIndexingStatus,
} from '@/service/datasets'
import type {
  SimpleDocumentDetail,
  docIndexingStatusResponse,
} from '@/models/datasets'
// 知识库公共能力
import style from '@/app/components/datasets/styles/style.module.css'
// 公共组件
import { useDatasetDetailContext } from '@/context/dataset-detail'
import SearchInput from '@/app/components/base/input/search-input'
import Loading from '@/app/components/base/loading'
import { asyncRunSafe } from '@/utils'
import { AddButton } from '@/app/components/base/button/add-button'
import { useProviderContext } from '@/context/provider-context'

// Custom page count is not currently supported.
const limit = 15

type IDocumentsProps = {
  datasetId: string
}

const Documents: FC<IDocumentsProps> = ({ datasetId }) => {
  const { t } = useTranslation()
  const { dataset } = useDatasetDetailContext()
  const router = useRouter()
  const { useXIYANRag } = useProviderContext()
  // 搜索值
  const [searchValue, setSearchValue] = useState<string>('')
  // 当前页码
  const [currPage, setCurrPage] = React.useState<number>(1)
  // 正在索引的文档
  const [indexingDocuments, setIndexingDocuments] = useState<Array<string>>([])

  // 嵌入模型是否可用
  const embeddingAvailable = !!dataset?.embedding_available
  // 文档列表
  const [documentsList, setDocumentsList] = useState<SimpleDocumentDetail[]>(
    [],
  )

  const query = useMemo(() => {
    return {
      page: currPage,
      limit,
      keyword: searchValue,
      fetch: '',
    }
  }, [currPage, searchValue])

  // 获取文档结果数据
  const {
    data: documentsRes,
    error,
    mutate,
  } = useSWR(
    {
      action: 'fetchDocuments',
      datasetId,
      params: query,
    },
    apiParams => fetchDocuments(omit(apiParams, 'action')),
  )
  // 文档总数
  const total = documentsRes?.total || 0
  // 是否正在加载
  const isLoading = !documentsRes && !error

  // 添加文件
  const routeToDocCreate = () => {
    router.push(`/datasets/${datasetId}/upload`)
  }
  // 批量更新正在索引的
  const bacthGetIndexStatus = async () => {
    if (indexingDocuments.length) {
      const [e, res] = await asyncRunSafe<docIndexingStatusResponse>(
        fetchDocumentsIndexingStatus({
          datasetId,
          document_id_list: indexingDocuments,
        }) as Promise<docIndexingStatusResponse>,
      )
      if (!e) {
        const completedSeg: Array<any> = []
        const unCompletedSeg: Array<string> = []
        Object.entries(res.document_status).forEach(([key, value]) => {
          if (judgeIsIndexing(value)) {
            unCompletedSeg.push(key)
          }
          else {
            completedSeg.push({
              ...value,
              key,
            })
          }
        })
        // 更新分段的信息
        for (const doc of documentsList) {
          const currentStatus = completedSeg.find(
            item => item.key === doc.id,
          )
          if (currentStatus) {
            changeIndexStatus(doc, currentStatus)
            // 更新字符数和错误信息
            if (currentStatus.word_count !== undefined)
              doc.word_count = currentStatus.word_count
            if (currentStatus.error !== undefined)
              doc.error = currentStatus.error
          }
        }
        setDocumentsList([...documentsList])
        // 变更还要索引的分段列表
        setIndexingDocuments(unCompletedSeg)
      }
    }
  }
  // 删除文档后更新列表
  const updateDocumentsListByDelete = () => {
    if (documentsList.length === 1 && currPage > 1)
      setCurrPage(currPage - 1)
    else mutate()
  }

  // 更新正在索引的文档
  useEffect(() => {
    if (documentsRes?.data) {
      setIndexingDocuments(
        documentsRes?.data
          ?.filter(doc => judgeIsIndexing(doc))
          ?.map(doc => doc.id) || [],
      )
    }
  }, [documentsRes])
  // 更新文档列表
  useEffect(() => {
    setDocumentsList(documentsRes?.data || [])
  }, [documentsRes])
  // 轮询获取正在索引的文档列表
  useEffect(() => {
    const intervalController = setInterval(() => {
      bacthGetIndexStatus()
    }, 1000)
    return () => clearInterval(intervalController)
  }, [indexingDocuments])

  return (
    <div className={style['left-part']}>
      <div className={style['left-title']}>
        {t('datasetDocuments.list.title')}
      </div>
      <div className={style['left-content']}>
        <div className="flex items-center justify-between flex-wrap mb-3">
          <SearchInput
            className="!w-[360px]"
            value={searchValue}
            onChange={e => setSearchValue(e)}
          />
          <div className="flex gap-2 justify-center items-center">
            {<RetryButton datasetId={datasetId} onRefresh={mutate} />}
            {embeddingAvailable && (
              <AddButton variant={'primary'} onClick={routeToDocCreate} className='shrink-0'>{t('datasetDocuments.list.addFile')}</AddButton>
            )}
          </div>
        </div>
        {isLoading
          ? (
            <Loading type="area" />
          )
          : (
            <List
              embeddingAvailable={embeddingAvailable}
              documents={documentsList || []}
              datasetId={datasetId}
              onUpdate={mutate}
              onDelete={updateDocumentsListByDelete}
            />
          )}
        <Pagination
          className="mt-3"
          align="end"
          current={currPage}
          hideOnSinglePage
          onChange={setCurrPage}
          total={total}
          pageSize={limit}
          showQuickJumper={false}
          showSizeChanger={false}
        />
      </div>
    </div>
  )
}

export default Documents
