/* eslint-disable multiline-ternary */
import type { FC } from 'react'
import React, { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useContext } from 'use-context-selector'

import { Switch } from 'antd'
import SegmentIndexTag from '../../../common/segment-index'
import StatusItem from '../../../common/status-indicator'
import DocumentTitle from '../../../common/document-title'
import { DocumentContext } from '../index'
import s from './style.module.css'
import type { SegmentDetailModel } from '@/models/datasets'

// 公共组件
import cn from '@/utils/classnames'
import Confirm from '@/app/components/base/confirm'
import Card from '@/app/components/base/card'
import { Target04 } from '@/app/components/base/icons/src/vender/line/general'
import Button from '@/app/components/base/button'
import { useProviderContext } from '@/context/provider-context'

const ProgressBar: FC<{ percent: number; loading: boolean }> = ({
  percent,
  loading,
}) => {
  return (
    <div className={s.progressWrapper}>
      <div className={cn(s.progress, loading ? s.progressLoading : '')}>
        <div
          className={s.progressInner}
          style={{
            width: `${loading ? 0 : (Math.min(percent, 1) * 100).toFixed(2)}%`,
          }}
        />
      </div>
      <div className={loading ? s.progressTextLoading : s.progressText}>
        {loading ? null : percent.toFixed(2)}
      </div>
    </div>
  )
}

export type UsageScene = 'doc' | 'hitTesting'

type ISegmentCardProps = {
  loading: boolean
  detail?: SegmentDetailModel & { document?: { name: string } }
  contentExternal?: string
  refSource?: {
    title: string
    uri: string
  }
  score?: number
  onClick?: () => void
  onChangeSwitch?: (segId: string, enabled: boolean) => Promise<void>
  onDelete?: (segId: string) => Promise<void>
  scene?: UsageScene
  className?: string
  archived?: boolean
  embeddingAvailable?: boolean
  category?: string | number
}

const SegmentCard: FC<ISegmentCardProps> = ({
  detail,
  contentExternal,
  refSource,
  score,
  onClick,
  onChangeSwitch,
  onDelete,
  loading = true,
  scene = 'doc',
  className = '',
  archived,
  embeddingAvailable,
}) => {
  const { t } = useTranslation()
  const { category } = useContext(DocumentContext)
  const {
    id,
    position,
    enabled,
    content,
    word_count,
    hit_count,
    index_node_hash,
    index_status,
    answer,
  } = detail || ({} as Required<ISegmentCardProps>['detail'])
  // 使用场景为文档
  const isDocScene = scene === 'doc'
  // 是否正在索引
  const isIndexing = index_status === 'indexing'
  // 是否显示确认删除弹窗
  const [showModal, setShowModal] = useState(false)

  const { useXIYANRag } = useProviderContext()

  // 卡片内容
  const renderContent = () => {
    if (loading) {
      // eslint-disable-next-line react/display-name
      return () => (
        <div className={cn(s.cardLoadingWrapper, s.cardLoadingIcon)}></div>
      )
    }
    if (answer) {
      // eslint-disable-next-line react/display-name
      return () => (
        <>
          <div className="flex mb-2">
            <div className="mr-2 text-[13px] font-semibold text-gray-400">
              Q
            </div>
            <div className="text-[13px]">{content}</div>
          </div>
          <div className="flex">
            <div className="mr-2 text-[13px] font-semibold text-gray-400">
              A
            </div>
            <div className="text-[13px]">{answer}</div>
          </div>
        </>
      )
    }

    if (contentExternal)
      return contentExternal

    return content
  }
  // 卡片头部
  const CardHeader = () =>
    isDocScene ? (
      <div className={s.segTitleWrapper}>
        {/* 分段标签 */}
        {!useXIYANRag ? <SegmentIndexTag positionId={position} /> : <span></span>}
        {!loading && (
          <div className={s.segStatusWrapper}>
            {/* 当前卡片状态 */}
            <StatusItem
              status={
                isIndexing ? 'indexing' : enabled ? 'enabled' : 'disabled'
              }
              textCls="text-gray-G2 text-[10px] leading-[20px]"
            />
            {/* 是否可以启用知识库卡片 */}
            {embeddingAvailable
              && !isIndexing
              && (
                <div className="inline-flex items-center">
                  {/* <Divider type="vertical" className="!h-2" /> */}
                  <div
                    onClick={(
                      e: React.MouseEvent<HTMLDivElement, MouseEvent>,
                    ) => e.stopPropagation()}
                    className="inline-flex items-center ml-2"
                  >
                    <Switch
                      size="small"
                      disabled={archived || detail?.status !== 'completed'}
                      value={enabled}
                      onChange={async (val) => {
                        await onChangeSwitch?.(id, val)
                      }}
                    />
                  </div>
                </div>
              )}
          </div>
        )}
      </div>
    ) : score !== null
      ? (
        <div className={s.hitTitleWrapper}>
          <Target04
            className={cn(
              s.commonIcon,
              loading ? '!text-gray-300' : '',
              '!w-3.5 !h-3.5',
            )}
          />
          <ProgressBar percent={score ?? 0} loading={loading} />
        </div>
      )
      : null

  return loading ? (
    <div className={cn(s.cardLoadingWrapper, s.cardLoadingIcon)}></div>
  ) : (
    <Card
      replaceClassName={cn(
        'group',
        s.segWrapper,
        isDocScene && !enabled && s['seg-disabled-wrapper'],
        className,
      )}
      title={CardHeader}
      descriptionLine={8.8}
      description={renderContent()}
    >
      {!loading ? (
        isDocScene ? (
          <div className={cn('!flex gap-4 justify-end !visible', s.segData)}>
            {/* 删除功能 */}
            {!archived && embeddingAvailable && !useXIYANRag && (
              <Button
                size={'small'}
                className="!rounded !min-w-[72px]"
                variant={'secondary-accent'}
                onClick={(e) => {
                  e.stopPropagation()
                  setShowModal(true)
                }}
              >
                {t('common.operation.delete')}
              </Button>
            )}
            {/* 查看 */}
            <Button
              variant={'primary'}
              size={'small'}
              className="!rounded !min-w-[72px]"
              onClick={onClick}
            >
              {t('common.operation.see')}
            </Button>
          </div>
        ) : (
          <div className={cn('w-full group-hover:bg-white')}>
            <div className="relative flex items-center w-full pb-1">
              <DocumentTitle
                name={detail?.document?.name || refSource?.title || ''}
                extension={
                  (detail?.document?.name || refSource?.title || '')
                    .split('.')
                    .pop() || 'txt'
                }
                wrapperCls="w-full"
                iconCls="!h-4 !w-4 !bg-contain"
                textCls="text-xs text-gray-700 !font-normal overflow-hidden whitespace-nowrap text-ellipsis"
              />
              {/* <TextButton variant={'primary'}>
                  {isExternal ? t('dataset.hit.viewDetail') : t('dataset.hit.viewChart')}
                  <ArrowUpRightIcon className="w-3 h-3 ml-1 stroke-current stroke-2" />
                </TextButton> */}
            </div>
          </div>
        )
      ) : (
        <></>
      )}
      {showModal && (
        <Confirm
          isShow={showModal}
          title={t('datasetDocuments.segment.delete')}
          content={t('datasetDocuments.segment.deleteTip')}
          onConfirm={async () => {
            await onDelete?.(id)
          }}
          onCancel={() => setShowModal(false)}
        />
      )}
    </Card>
  )
}

export default SegmentCard
