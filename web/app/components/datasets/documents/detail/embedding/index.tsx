import type { FC } from 'react'
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react'
import useSWR from 'swr'
import { useContext } from 'use-context-selector'
import { useTranslation } from 'react-i18next'
import { omit } from 'lodash-es'
import { Divider, Progress } from 'antd'
import SegmentCard from '../segment-card'
import { DocumentContext } from '../index'
import s from './style.module.css'

import type {
  FullDocumentDetail,
  IndexingStatusResponse,
  ProcessRuleResponse,
} from '@/models/datasets'
import type { CommonResponse } from '@/models/common'
import {
  fetchIndexingStatus as doFetchIndexingStatus,
  fetchProcessRule,
  pauseDocIndexing,
  resumeDocIndexing,
} from '@/service/datasets'
import StopEmbeddingModal from '@/app/components/datasets/create/stop-embedding-modal'
import datasetStyle from '@/app/components/datasets/styles/style.module.css'
import FieldInfo from '@/app/components/datasets/common/field-info'
import { judgeDocIndexEnd } from '@/app/components/datasets/utils'

import { asyncRunSafe, sleep } from '@/utils'
import cn from '@/utils/classnames'
import { ToastContext } from '@/app/components/base/toast'
import Button from '@/app/components/base/button'
import { ArrowDown } from '@/app/components/base/icons/src/vender/line/arrows'
import DatasetDetailContext from '@/context/dataset-detail'
import { useProviderContext } from '@/context/provider-context'

type Props = {
  detail?: FullDocumentDetail
  datasetId?: string
  documentId?: string
  detailUpdate: VoidFunction
  onSeeDocumentInfo: () => void
}

const RuleDetail: FC<{
  sourceData?: ProcessRuleResponse
  docName?: string
  layout: 'col' | 'grid'
}> = ({ sourceData, docName, layout }) => {
  const { t } = useTranslation()
  const { dataset: currentDataset } = useContext(DatasetDetailContext)
  // 分段规则映射
  const segmentationRuleMap = {
    docName: t('datasetDocuments.embedding.docName'),
    automatic: t('datasetDocuments.embedding.automatic'),
    mode: t('datasetDocuments.embedding.mode'),
    embeddingModel: t('datasetDocuments.embedding.embeddingModel'),
  }
  // 获取分段规则名称
  const getRuleName = useCallback(
    (key: string) => {
      if (key === 'remove_extra_spaces')
        return t('datasetCreation.stepTwo.removeExtraSpaces')

      if (key === 'remove_urls_emails')
        return t('datasetCreation.stepTwo.removeUrlEmails')

      if (key === 'remove_stopwords')
        return t('datasetCreation.stepTwo.removeStopwords')
    },
    [t],
  )
  // 获取分段规则值
  const getValue = useCallback(
    (field: string) => {
      let value: string | number | undefined = '-'
      switch (field) {
        case 'docName':
          value = docName
          break
        case 'mode':
          value
            = sourceData?.mode === 'automatic'
              ? (t('datasetDocuments.embedding.auto') as string)
              : (t('datasetDocuments.embedding.custom') as string)
          break
        case 'segmentLength':
          value = sourceData?.rules?.segmentation?.max_tokens
          break
        case 'embeddingModel':
          value = currentDataset?.embedding_model
          break
        default:
          value
            = sourceData?.mode === 'automatic'
              ? (t('datasetDocuments.embedding.auto') as string)
              : sourceData?.rules?.pre_processing_rules
                // eslint-disable-next-line array-callback-return
                ?.map((rule) => {
                  if (rule.enabled)
                    return getRuleName(rule.id)
                })
                .filter(Boolean)
                .join(';')
          break
      }
      return value
    },
    [docName, sourceData, t, getRuleName],
  )

  if (layout === 'col') {
    return (
      <div className="flex flex-col first:mt-0">
        {Object.keys(segmentationRuleMap).map((field) => {
          return (
            <FieldInfo
              key={field}
              label={
                segmentationRuleMap[field as keyof typeof segmentationRuleMap]
              }
              displayedValue={String(getValue(field))}
            />
          )
        })}
      </div>
    )
  }
  if (layout === 'grid') {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 ">
        {Object.keys(segmentationRuleMap).map((field) => {
          return (
            <FieldInfo
              key={field}
              label={
                segmentationRuleMap[field as keyof typeof segmentationRuleMap]
              }
              displayedValue={String(getValue(field))}
            />
          )
        })}
      </div>
    )
  }
}

const EmbeddingDetail: FC<Props> = ({
  detail,
  onSeeDocumentInfo,
  datasetId: dstId,
  documentId: docId,
  detailUpdate,
}) => {
  const { t } = useTranslation()
  const { notify } = useContext(ToastContext)
  const { datasetId = '', documentId = '' } = useContext(DocumentContext)
  const localDatasetId = dstId ?? datasetId
  const localDocumentId = docId ?? documentId

  // 是否停止查询文档索引状态
  const isStopQuery = useRef(false)
  // 文档索引状态
  const [indexingStatusDetail, setIndexingStatusDetail]
    = useState<IndexingStatusResponse>()
  // 是否显示停止嵌入弹窗
  const [showModal, setShowModal] = useState(false)
  const modalCloseHandle = () => setShowModal(false)
  // 获取文档规则
  const { data: ruleDetail, error: ruleError } = useSWR(
    {
      action: 'fetchProcessRule',
      params: { documentId: localDocumentId },
    },
    apiParams => fetchProcessRule(omit(apiParams, 'action')),
    {
      revalidateOnFocus: false,
    },
  )

  // 更新文档索引状态
  const fetchIndexingStatus = useCallback(async () => {
    const status = await doFetchIndexingStatus({
      datasetId: localDatasetId,
      documentId: localDocumentId,
    })
    setIndexingStatusDetail(status)
    return status
  }, [localDatasetId, localDocumentId])
  // 停止查询文档状态
  const stopQueryStatus = useCallback(() => {
    isStopQuery.current = true
  }, [])
  // 开始查询文档状态
  const startQueryStatus = useCallback(async () => {
    if (isStopQuery.current)
      return
    const queryStatus = async () => {
      try {
        const indexingStatusDetail = await fetchIndexingStatus()
        if (
          judgeDocIndexEnd({
            index_status: 'completed',
            indexing_status: indexingStatusDetail.indexing_status,
          })
        ) {
          stopQueryStatus()
          return
        }

        await sleep(2500)
        await startQueryStatus()
      }
      catch (e) {
        await sleep(2500)
        await startQueryStatus()
      }
    }
    detailUpdate()
    queryStatus()
  }, [detailUpdate, fetchIndexingStatus, stopQueryStatus])

  // 是否是私有知识库
  const { useXIYANRag } = useProviderContext()

  useEffect(() => {
    isStopQuery.current = false
    startQueryStatus()
    return () => {
      stopQueryStatus()
    }
  }, [startQueryStatus, stopQueryStatus])

  // 嵌入状态
  const isEmbeddingWatting = useMemo(
    () => ['waiting'].includes(indexingStatusDetail?.indexing_status || ''),
    [indexingStatusDetail],
  )
  const isEmbedding = useMemo(
    () =>
      ['indexing', 'splitting', 'parsing', 'cleaning'].includes(
        indexingStatusDetail?.indexing_status || '',
      ),
    [indexingStatusDetail],
  )
  const isEmbeddingCompleted = useMemo(
    () => ['completed'].includes(indexingStatusDetail?.indexing_status || ''),
    [indexingStatusDetail],
  )
  const isEmbeddingPaused = useMemo(
    () => ['paused'].includes(indexingStatusDetail?.indexing_status || ''),
    [indexingStatusDetail],
  )
  const isEmbeddingError = useMemo(
    () => ['error'].includes(indexingStatusDetail?.indexing_status || ''),
    [indexingStatusDetail],
  )
  // 嵌入百分比
  const percent = useMemo(() => {
    // const completedCount = indexingStatusDetail?.completed_segments || 0
    // const totalCount = indexingStatusDetail?.total_segments || 0
    // if (totalCount === 0)
    //   return 0
    // const percent = Math.round((completedCount * 100) / totalCount)
    // const percent = indexingStatusDetail?.doc_process
    return indexingStatusDetail?.doc_process
  }, [indexingStatusDetail])

  // 停止/继续嵌入操作
  const handleSwitch = async () => {
    const opApi = isEmbedding ? pauseDocIndexing : resumeDocIndexing
    const [e] = await asyncRunSafe<CommonResponse>(
      opApi({
        datasetId: localDatasetId,
        documentId: localDocumentId,
      }) as Promise<CommonResponse>,
    )
    if (!e) {
      notify({
        type: 'success',
        message: t('common.actionMsg.modifiedSuccessfully'),
      })
      setIndexingStatusDetail(prev => prev
        ? {
          ...prev,
          indexing_status: 'paused',
        }
        : undefined)
      if (!isEmbedding) {
        isStopQuery.current = false
        startQueryStatus()
      }
    }
  }

  return (
    <>
      {/* 头部及操作按钮  */}
      <div className={cn(datasetStyle['left-title'], 'justify-between')}>
        {isEmbedding && t('datasetDocuments.embedding.processing')}
        {isEmbeddingCompleted && t('datasetDocuments.embedding.completed')}
        {isEmbeddingPaused && t('datasetDocuments.embedding.paused')}
        {isEmbeddingError && t('datasetDocuments.embedding.error')}
        {isEmbeddingWatting && t('datasetDocuments.embedding.waiting')}
        {isEmbedding && (
          <Button variant={'primary'} onClick={handleSwitch}>
            {t('datasetDocuments.embedding.stop')}
          </Button>
        )}
        {isEmbeddingPaused && (
          <Button variant={'primary'} onClick={handleSwitch}>
            {t('datasetDocuments.embedding.resume')}
          </Button>
        )}
      </div>
      <div className={datasetStyle['left-content']}>
        {/* progress bar */}
        <Progress
          showInfo={false}
          percent={percent}
          className="!leading-[8px]"
        ></Progress>
        <div className={s.progressData}>
          <div>
            {t('datasetDocuments.embedding.segments')}{' '}
            {indexingStatusDetail?.completed_segments}/
            {indexingStatusDetail?.total_segments} · {percent}%
          </div>
        </div>
        {/* 规则详情 */}
        <RuleDetail
          sourceData={ruleDetail}
          docName={detail?.name}
          layout="grid"
        ></RuleDetail>
        {/* 加载效果 */}
        {
          <>
            <Divider className="!mt-6 !mb-8" />
            <div className={s.previewTip}>
              {t('datasetDocuments.embedding.previewTip')}
            </div>
            <div className={s.cardWrapper}>
              {[1, 2, 3, 4].map((v, index) => (
                <SegmentCard
                  key={index}
                  loading={true}
                  detail={{ position: v } as any}
                />
              ))}
            </div>
          </>
        }
        {/* 文档详情 */}
        <Button
          className="w-[110px]"
          variant="info"
          size="large"
          onClick={onSeeDocumentInfo}
        >
          {t('datasetDocuments.embedding.files')}
          <ArrowDown className="w-4 h-4" />
        </Button>
      </div>
      <StopEmbeddingModal
        show={showModal}
        onConfirm={handleSwitch}
        onHide={modalCloseHandle}
      />
    </>
  )
}

export default React.memo(EmbeddingDetail)
