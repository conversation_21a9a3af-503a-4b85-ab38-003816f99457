import type { DocumentDisplayStatus, DocumentIndexingStatus, IndexingStatus, SegmentDetailModel, SimpleDocumentDetail } from '@/models/datasets'

// 文档是否可操作
export const judgeDocOperable = (doc: {
  display_status: DocumentDisplayStatus
  archived: boolean
}) => {
  return ['available', 'enabled', 'disabled'].includes(doc.display_status) && !doc.archived
}
// 判断文档是否索引完成
export const judgeDocIndexEnd = (doc: {
  index_status: IndexingStatus
  indexing_status: DocumentIndexingStatus
}) => {
  return doc.index_status !== 'indexing' && ['completed', 'error', 'paused'].includes(doc.indexing_status)
}
// 是否正在索引
export const judgeIsIndexing = (doc: {
  index_status: IndexingStatus
  display_status: DocumentDisplayStatus
}) => {
  return doc.index_status === 'indexing' || ['indexing', 'queuing'].includes(doc.display_status)
}
// 判断是否正在嵌入
export const judgeIsEmbedding = (doc?: SimpleDocumentDetail) => {
  return ['queuing', 'indexing', 'paused'].includes((doc?.display_status || '').toLowerCase())
}
// 判断分段是否正在索引
export const judgeIsIndexingSeg = (seg: SegmentDetailModel) => {
  return seg.index_status === 'indexing'
}
// 判断分段是否正在索引 通过值
export const judgeIsIndexingSegByValue = (value: IndexingStatus) => {
  return value === 'indexing'
}

// 变更文档索引状态
export const changeIndexStatus = (doc: SimpleDocumentDetail, status: {
  index_status: IndexingStatus
  indexing_status: DocumentIndexingStatus
  display_status: DocumentDisplayStatus
  word_count?: number
  error?: string | null
}) => {
  doc.index_status = status.index_status
  doc.indexing_status = status.indexing_status
  doc.display_status = status.display_status
  if (status.word_count !== undefined)
    doc.word_count = status.word_count
  if (status.error !== undefined)
    doc.error = status.error
}
// 文档状态
export function getDocStatus(doc: SimpleDocumentDetail): DocumentDisplayStatus {
  return judgeIsIndexing(doc) ? 'indexing' : doc.display_status
}
